export declare const RotateInData: {
    RotateInDownLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    RotateInDownRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    RotateInUpLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    RotateInUpRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
};
export declare const RotateOutData: {
    RotateOutDownLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    RotateOutDownRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    RotateOutUpLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    RotateOutUpRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    translateY: string;
                    rotate: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
};
export declare const RotateIn: {
    RotateInDownLeft: {
        style: string;
        duration: number;
    };
    RotateInDownRight: {
        style: string;
        duration: number;
    };
    RotateInUpLeft: {
        style: string;
        duration: number;
    };
    RotateInUpRight: {
        style: string;
        duration: number;
    };
};
export declare const RotateOut: {
    RotateOutDownLeft: {
        style: string;
        duration: number;
    };
    RotateOutDownRight: {
        style: string;
        duration: number;
    };
    RotateOutUpLeft: {
        style: string;
        duration: number;
    };
    RotateOutUpRight: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Rotate.web.d.ts.map