import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SoftCard } from './SoftCard';
import { theme } from '../theme';
import { Item } from '../types/Item';

interface ItemCardProps {
  item: Item;
  onPress: () => void;
  onLike: () => void;
}

const { width } = Dimensions.get('window');
const cardWidth = (width - theme.spacing['2xl'] * 3) / 2;

export const ItemCard: React.FC<ItemCardProps> = ({ item, onPress, onLike }) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <SoftCard style={[styles.card, { width: cardWidth }] as any} padding="lg">
        <View style={styles.imageContainer}>
          <Image source={{ uri: item.imageUrl }} style={styles.image} />
          {item.timeLeft && (
            <View style={styles.timeLeftBadge}>
              <Text style={styles.timeLeftText}>{item.timeLeft}</Text>
            </View>
          )}
          <TouchableOpacity style={styles.likeButton} onPress={onLike}>
            <Ionicons
              name={item.isLiked ? 'heart' : 'heart-outline'}
              size={20}
              color={item.isLiked ? theme.colors.primaryAccent : theme.colors.secondaryText}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Text style={styles.title} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.seller} numberOfLines={1}>
            by {item.seller.name}
          </Text>

          <View style={styles.priceContainer}>
            <View style={styles.priceRow}>
              <Ionicons
                name="diamond-outline"
                size={16}
                color={theme.colors.primaryAccent}
              />
              <Text style={styles.price}>
                {item.price} {item.currency}
              </Text>
            </View>
            <Text style={styles.priceLabel}>-40%</Text>
          </View>
        </View>
      </SoftCard>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: theme.spacing.lg,
  },

  imageContainer: {
    position: 'relative',
    marginBottom: theme.spacing.md,
  },

  image: {
    width: '100%',
    height: 120,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.borderGray,
  },

  timeLeftBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    left: theme.spacing.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },

  timeLeftText: {
    color: theme.colors.accentText,
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
  },

  likeButton: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: theme.colors.accentText,
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: theme.colors.shadowDark,
    shadowOffset: {
      width: 2,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },

  content: {
    flex: 1,
  },

  title: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.xs,
  },

  seller: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.secondaryText,
    marginBottom: theme.spacing.sm,
  },

  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  price: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semiBold,
    color: theme.colors.primaryText,
    marginLeft: theme.spacing.xs,
  },

  priceLabel: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.secondaryText,
  },
});
