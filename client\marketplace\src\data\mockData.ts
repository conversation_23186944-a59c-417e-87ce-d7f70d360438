import { Item, Category } from '../types/Item';

export const mockItems: Item[] = [
  {
    id: '1',
    title: 'Digital 3D art',
    description: 'Beautiful abstract 3D artwork with stunning visual effects',
    price: 0.48,
    currency: 'ETH',
    imageUrl: 'https://picsum.photos/300/200?random=1',
    seller: {
      id: 'seller1',
      name: '<PERSON>',
      username: '@liamcarter',
    },
    category: 'Digital Art',
    timeLeft: '2h 34m',
    isLiked: false,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    title: 'Digital 3D art',
    description: 'Colorful abstract composition with modern design elements',
    price: 0.48,
    currency: 'ETH',
    imageUrl: 'https://picsum.photos/300/200?random=2',
    seller: {
      id: 'seller2',
      name: '<PERSON>',
      username: '@liamcarter',
    },
    category: 'Digital Art',
    timeLeft: '2h 34m',
    isLiked: true,
    createdAt: '2024-01-14',
  },
  {
    id: '3',
    title: 'Digital 3D art',
    description: 'Minimalist 3D sculpture with elegant curves',
    price: 0.48,
    currency: 'ETH',
    imageUrl: 'https://picsum.photos/300/200?random=3',
    seller: {
      id: 'seller3',
      name: '<PERSON>',
      username: '@liamcarter',
    },
    category: 'Digital Art',
    timeLeft: '2h 34m',
    isLiked: false,
    createdAt: '2024-01-13',
  },
  {
    id: '4',
    title: 'Digital 3D art',
    description: 'Vibrant digital artwork with dynamic composition',
    price: 0.48,
    currency: 'ETH',
    imageUrl: 'https://picsum.photos/300/200?random=4',
    seller: {
      id: 'seller4',
      name: 'Liam Carter',
      username: '@liamcarter',
    },
    category: 'Digital Art',
    timeLeft: '2h 34m',
    isLiked: false,
    createdAt: '2024-01-12',
  },
  {
    id: '5',
    title: 'Digital 3D art',
    description: 'Abstract geometric patterns in 3D space',
    price: 0.48,
    currency: 'ETH',
    imageUrl: 'https://picsum.photos/300/200?random=5',
    seller: {
      id: 'seller5',
      name: 'Liam Carter',
      username: '@liamcarter',
    },
    category: 'Digital Art',
    timeLeft: '2h 34m',
    isLiked: true,
    createdAt: '2024-01-11',
  },
  {
    id: '6',
    title: 'Digital 3D art',
    description: 'Futuristic digital sculpture with metallic finish',
    price: 0.48,
    currency: 'ETH',
    imageUrl: 'https://picsum.photos/300/200?random=6',
    seller: {
      id: 'seller6',
      name: 'Liam Carter',
      username: '@liamcarter',
    },
    category: 'Digital Art',
    timeLeft: '2h 34m',
    isLiked: false,
    createdAt: '2024-01-10',
  },
];

export const mockCategories: Category[] = [
  { id: '1', name: 'Items', icon: 'grid-outline' },
  { id: '2', name: 'Categories', icon: 'apps-outline' },
  { id: '3', name: 'Collections', icon: 'bookmark-outline' },
];
