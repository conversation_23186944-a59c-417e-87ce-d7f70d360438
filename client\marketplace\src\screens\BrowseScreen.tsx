import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SoftCard, ItemCard } from '../components';
import { theme } from '../theme';
import { Item, Category } from '../types/Item';
import { mockItems, mockCategories } from '../data/mockData';

interface BrowseScreenProps {
  navigation: any;
}

export const BrowseScreen: React.FC<BrowseScreenProps> = ({ navigation }) => {
  const [items, setItems] = useState<Item[]>(mockItems);
  const [selectedCategory, setSelectedCategory] = useState<string>('Items');
  const [searchQuery, setSearchQuery] = useState('');

  const handleItemPress = (item: Item) => {
    // Navigate to item detail screen
    console.log('Item pressed:', item.title);
  };

  const handleLike = (itemId: string) => {
    setItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? { ...item, isLiked: !item.isLiked } : item
      )
    );
  };

  const renderCategoryTab = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={[
        styles.categoryTab,
        selectedCategory === item.name && styles.activeCategoryTab,
      ]}
      onPress={() => setSelectedCategory(item.name)}
    >
      <Ionicons
        name={item.icon as any}
        size={16}
        color={
          selectedCategory === item.name
            ? theme.colors.accentText
            : theme.colors.secondaryText
        }
      />
      <Text
        style={[
          styles.categoryText,
          selectedCategory === item.name && styles.activeCategoryText,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderItem = ({ item, index }: { item: Item; index: number }) => (
    <View style={index % 2 === 0 ? styles.leftColumn : styles.rightColumn}>
      <ItemCard
        item={item}
        onPress={() => handleItemPress(item)}
        onLike={() => handleLike(item.id)}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Discover gallery</Text>
        
        <View style={styles.categoriesContainer}>
          <FlatList
            data={mockCategories}
            renderItem={renderCategoryTab}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>
      </View>

      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.itemsList}
        showsVerticalScrollIndicator={false}
        columnWrapperStyle={styles.row}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.primaryBackground,
  },
  
  header: {
    paddingHorizontal: theme.spacing['2xl'],
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  
  title: {
    fontSize: theme.typography.heading1.fontSize,
    fontWeight: theme.typography.heading1.fontWeight,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.lg,
  },
  
  categoriesContainer: {
    marginBottom: theme.spacing.md,
  },
  
  categoriesList: {
    paddingRight: theme.spacing['2xl'],
  },
  
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
    marginRight: theme.spacing.md,
    borderRadius: theme.borderRadius.pill,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.borderGray,
  },
  
  activeCategoryTab: {
    backgroundColor: theme.colors.primaryAccent,
    borderColor: theme.colors.primaryAccent,
    shadowColor: theme.colors.primaryAccent,
    shadowOffset: {
      width: 2,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  
  categoryText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.secondaryText,
    marginLeft: theme.spacing.xs,
  },
  
  activeCategoryText: {
    color: theme.colors.accentText,
  },
  
  itemsList: {
    paddingHorizontal: theme.spacing['2xl'],
    paddingBottom: theme.spacing['4xl'],
  },
  
  row: {
    justifyContent: 'space-between',
  },
  
  leftColumn: {
    flex: 1,
    marginRight: theme.spacing.md,
  },
  
  rightColumn: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
});
