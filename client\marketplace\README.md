# CircleFree Marketplace App

A beautiful marketplace app built with Expo and React Native, featuring a soft UI design inspired by the provided design guide.

## Features

- **Authentication**: Login and registration screens with form validation
- **Browse Items**: Grid layout for browsing marketplace items with category filtering
- **Post Items**: Create new marketplace listings with image upload
- **Soft UI Design**: Implements the design guide with warm colors, rounded corners, and soft shadows

## Design System

The app follows a "Soft UI" design aesthetic with:

- **Color Palette**: Warm off-white backgrounds with terracotta accent colors
- **Typography**: Clean sans-serif fonts with clear hierarchy
- **Components**: Highly rounded corners (24px+) with soft shadows
- **Spacing**: Generous padding and margins for a clean, breathable layout

## Tech Stack

- **Expo SDK 53**
- **React Native 0.79**
- **TypeScript**
- **React Navigation 7**
- **Expo Vector Icons**
- **Expo Image Picker**

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Expo CLI (`npm install -g @expo/cli`)
- Expo Go app on your mobile device (for testing)

### Installation

1. Navigate to the project directory:
   ```bash
   cd client/marketplace
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   # or
   npx expo start
   ```

4. Scan the QR code with Expo Go app (Android) or Camera app (iOS)

### Available Scripts

- `npm start` - Start the Expo development server
- `npm run android` - Start on Android emulator
- `npm run ios` - Start on iOS simulator (macOS only)
- `npm run web` - Start web version

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── SoftCard.tsx    # Card component with soft UI styling
│   ├── SoftButton.tsx  # Button component with pill shape
│   ├── SoftInput.tsx   # Input component with inset styling
│   └── ItemCard.tsx    # Marketplace item card
├── screens/            # App screens
│   ├── LoginScreen.tsx
│   ├── RegisterScreen.tsx
│   ├── BrowseScreen.tsx
│   └── PostItemScreen.tsx
├── navigation/         # Navigation configuration
│   ├── AuthStack.tsx
│   ├── MainTabs.tsx
│   └── AppNavigator.tsx
├── theme/             # Design system
│   ├── colors.ts
│   ├── typography.ts
│   ├── spacing.ts
│   └── index.ts
├── types/             # TypeScript type definitions
└── data/              # Mock data for development
```

## Key Components

### SoftCard
Implements the signature soft UI card design with:
- Large border radius (24px)
- Soft shadow effects
- Warm background colors

### SoftButton
Pill-shaped buttons with:
- Primary variant (terracotta background)
- Secondary variant (outlined)
- Loading states

### SoftInput
Form inputs with:
- Inset shadow effect
- Rounded corners
- Error state handling

### ItemCard
Marketplace item display with:
- Image preview
- Like button
- Price display with ETH currency
- Seller information

## Design Guide Implementation

The app closely follows the provided design guide:

1. **Color Palette**: Uses the exact colors specified (#F9F6F4, #F2EEEB, #E88B5B, etc.)
2. **Typography**: Implements the 4-level hierarchy (Page Titles, Section Titles, Body Text, Metadata)
3. **Shadows**: Complex shadow system with both dark and light shadows for depth
4. **Spacing**: Generous padding (20-32px) and consistent margins
5. **Border Radius**: Large radius values (24px+) for the soft, rounded aesthetic

## Future Enhancements

- User profile management
- Real-time messaging
- Payment integration
- Advanced search and filtering
- Push notifications
- Favorites and collections
- User reviews and ratings

## Contributing

1. Follow the established design system
2. Maintain TypeScript strict mode
3. Use the provided theme constants
4. Test on both iOS and Android
5. Ensure accessibility compliance
