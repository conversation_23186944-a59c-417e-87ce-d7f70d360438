import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { SoftCard, SoftButton, SoftInput } from '../components';
import { theme } from '../theme';

interface PostItemScreenProps {
  navigation: any;
}

export const PostItemScreen: React.FC<PostItemScreenProps> = ({ navigation }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    category: '',
  });
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
    price?: string;
    category?: string;
    image?: string;
  }>({});

  const categories = [
    'Digital Art',
    'Photography',
    'Music',
    'Video',
    'Collectibles',
    'Sports',
    'Trading Cards',
    'Utility',
  ];

  const updateFormData = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to upload images.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setSelectedImage(result.assets[0].uri);
      if (errors.image) {
        setErrors(prev => ({ ...prev, image: undefined }));
      }
    }
  };

  const validateForm = () => {
    const newErrors: typeof errors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    
    if (!formData.price) {
      newErrors.price = 'Price is required';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Please enter a valid price';
    }
    
    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }
    
    if (!selectedImage) {
      newErrors.image = 'Please select an image';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      Alert.alert('Success', 'Item posted successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    }, 2000);
  };

  const renderCategorySelector = () => (
    <View style={styles.categoryContainer}>
      <Text style={styles.label}>Category</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesList}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              formData.category === category && styles.selectedCategoryChip,
            ]}
            onPress={() => updateFormData('category', category)}
          >
            <Text
              style={[
                styles.categoryChipText,
                formData.category === category && styles.selectedCategoryChipText,
              ]}
            >
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      {errors.category && (
        <Text style={styles.errorText}>{errors.category}</Text>
      )}
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Post New Item</Text>
          <Text style={styles.subtitle}>Share your creation with the world</Text>
        </View>

        <SoftCard style={styles.formCard}>
          {/* Image Upload */}
          <View style={styles.imageSection}>
            <Text style={styles.label}>Item Image</Text>
            <TouchableOpacity style={styles.imageUpload} onPress={pickImage}>
              {selectedImage ? (
                <Image source={{ uri: selectedImage }} style={styles.uploadedImage} />
              ) : (
                <View style={styles.uploadPlaceholder}>
                  <Ionicons
                    name="camera-outline"
                    size={40}
                    color={theme.colors.secondaryText}
                  />
                  <Text style={styles.uploadText}>Tap to upload image</Text>
                </View>
              )}
            </TouchableOpacity>
            {errors.image && (
              <Text style={styles.errorText}>{errors.image}</Text>
            )}
          </View>

          <SoftInput
            label="Title"
            value={formData.title}
            onChangeText={(value) => updateFormData('title', value)}
            placeholder="Enter item title"
            error={errors.title}
          />

          <SoftInput
            label="Description"
            value={formData.description}
            onChangeText={(value) => updateFormData('description', value)}
            placeholder="Describe your item"
            multiline
            numberOfLines={4}
            error={errors.description}
          />

          <SoftInput
            label="Price (ETH)"
            value={formData.price}
            onChangeText={(value) => updateFormData('price', value)}
            placeholder="0.00"
            keyboardType="decimal-pad"
            error={errors.price}
          />

          {renderCategorySelector()}

          <SoftButton
            title="Post Item"
            onPress={handleSubmit}
            loading={loading}
            style={styles.submitButton}
          />
        </SoftCard>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.primaryBackground,
  },
  
  scrollContent: {
    flexGrow: 1,
    padding: theme.spacing['2xl'],
  },
  
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing['3xl'],
  },
  
  title: {
    fontSize: theme.typography.heading1.fontSize,
    fontWeight: theme.typography.heading1.fontWeight,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.sm,
  },
  
  subtitle: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.secondaryText,
    textAlign: 'center',
  },
  
  formCard: {
    marginBottom: theme.spacing.xl,
  },
  
  imageSection: {
    marginBottom: theme.spacing.lg,
  },
  
  label: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.sm,
  },
  
  imageUpload: {
    height: 200,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  
  uploadedImage: {
    width: '100%',
    height: '100%',
  },
  
  uploadPlaceholder: {
    flex: 1,
    backgroundColor: theme.colors.borderGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  uploadText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.secondaryText,
    marginTop: theme.spacing.sm,
  },
  
  categoryContainer: {
    marginBottom: theme.spacing.lg,
  },
  
  categoriesList: {
    paddingRight: theme.spacing.lg,
  },
  
  categoryChip: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
    marginRight: theme.spacing.sm,
    borderRadius: theme.borderRadius.pill,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.borderGray,
  },
  
  selectedCategoryChip: {
    backgroundColor: theme.colors.primaryAccent,
    borderColor: theme.colors.primaryAccent,
  },
  
  categoryChipText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.secondaryText,
    fontWeight: theme.typography.fontWeight.medium,
  },
  
  selectedCategoryChipText: {
    color: theme.colors.accentText,
  },
  
  submitButton: {
    marginTop: theme.spacing.lg,
  },
  
  errorText: {
    fontSize: theme.typography.fontSize.xs,
    color: '#FF6B6B',
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.sm,
  },
});
